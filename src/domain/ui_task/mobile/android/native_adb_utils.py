#!/usr/bin/env python3
"""
原生ADB命令工具模块

使用subprocess调用原生adb命令，替代adbutils库
提供更稳定的adb命令执行
"""

import re
import subprocess
import time
from typing import Tuple, List

from loguru import logger


class NativeADBUtils:
    """原生ADB命令工具类"""

    @staticmethod
    def execute_adb_command(command: str, timeout: int = 30) -> Tuple[bool, str]:
        """
        执行原生ADB命令，支持重试机制

        Args:
            command: ADB命令字符串
            timeout: 超时时间（秒）

        Returns:
            Tuple[success, output]: 执行结果和输出
        """
        max_retries = 2  # 最多重试2次

        for attempt in range(max_retries + 1):  # 总共尝试3次（1次原始 + 2次重试）
            try:
                if attempt > 0:
                    logger.debug(f"Retrying ADB command (attempt {attempt + 1}/{max_retries + 1}): {command}")
                else:
                    logger.debug(f"Executing ADB command: {command}")

                # 执行命令
                result = subprocess.run(
                    command,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=timeout
                )
                logger.debug(f"ADB command result: {result}")

                # 检查返回码
                if result.returncode == 0:
                    output = result.stdout.strip()
                    if attempt > 0:
                        logger.info(f"ADB command succeeded on retry {attempt}: {command}")
                    return True, output
                else:
                    error_msg = result.stderr.strip() or result.stdout.strip()
                    if attempt < max_retries:
                        logger.warning(f"ADB command failed (attempt {attempt + 1}/{max_retries + 1}), will retry: {error_msg}")
                        time.sleep(1)  # 重试前等待1秒
                        continue
                    else:
                        logger.error(f"ADB command failed after {max_retries + 1} attempts: {error_msg}")
                        return False, error_msg

            except subprocess.TimeoutExpired:
                if attempt < max_retries:
                    logger.warning(f"ADB command timeout (attempt {attempt + 1}/{max_retries + 1}), will retry: {command}")
                    time.sleep(1)
                    continue
                else:
                    logger.error(f"ADB command timeout after {max_retries + 1} attempts: {command}")
                    return False, "Command timeout"
            except Exception as e:
                if attempt < max_retries:
                    logger.warning(f"ADB command error (attempt {attempt + 1}/{max_retries + 1}), will retry: {str(e)}")
                    time.sleep(1)
                    continue
                else:
                    logger.error(f"ADB command error after {max_retries + 1} attempts: {str(e)}")
                    return False, str(e)

        # 这行代码理论上不会被执行到，但为了安全起见保留
        return False, "Unexpected error in retry logic"

    @staticmethod
    def connect_device(device_id: str) -> bool:
        """
        连接远程设备
        
        Args:
            device_id: 设备ID (IP:PORT格式)
            
        Returns:
            是否连接成功
        """
        command = f"adb connect {device_id}"
        success, output = NativeADBUtils.execute_adb_command(command)

        if success and ("connected" in output.lower() or "already connected" in output.lower()):
            logger.info(f"✅ Successfully connected to device: {device_id}")
            return True
        else:
            logger.error(f"❌ Failed to connect to device {device_id}: {output}")
            return False

    @staticmethod
    def disconnect_device(device_id: str) -> bool:
        """
        断开设备连接
        
        Args:
            device_id: 设备ID
            
        Returns:
            是否断开成功
        """
        command = f"adb disconnect {device_id}"
        success, output = NativeADBUtils.execute_adb_command(command)

        if success:
            logger.info(f"✅ Successfully disconnected device: {device_id}")
            return True
        else:
            logger.error(f"❌ Failed to disconnect device {device_id}: {output}")
            return False

    @staticmethod
    def is_device_available(device_id: str) -> bool:
        """
        检查设备是否可用
        
        Args:
            device_id: 设备ID
            
        Returns:
            设备是否可用
        """
        command = f"adb -s {device_id} shell echo 'test'"
        success, output = NativeADBUtils.execute_adb_command(command, timeout=10)

        if success and "test" in output:
            logger.debug(f"✅ Device {device_id} is responsive")
            return True
        else:
            logger.error(f"❌ Device {device_id} is not responsive: {output}")
            return False

    @staticmethod
    def list_devices() -> List[str]:
        """
        列出所有连接的设备
        
        Returns:
            设备ID列表
        """
        command = "adb devices"
        success, output = NativeADBUtils.execute_adb_command(command)

        if not success:
            logger.error(f"Failed to list devices: {output}")
            return []

        devices = []
        lines = output.split('\n')
        for line in lines[1:]:  # 跳过第一行标题
            if line.strip() and '\t' in line:
                device_id = line.split('\t')[0].strip()
                status = line.split('\t')[1].strip()
                if status == "device":  # 只返回可用设备
                    devices.append(device_id)

        logger.info(f"📱 Available devices: {devices}")
        return devices

    @staticmethod
    def shell_command(device_id: str, command: str, timeout: int = 30) -> Tuple[bool, str]:
        """
        在设备上执行shell命令
        
        Args:
            device_id: 设备ID
            command: shell命令
            timeout: 超时时间（秒）
            
        Returns:
            Tuple[success, output]: 执行结果和输出
        """
        adb_command = f"adb -s {device_id} shell {command}"
        return NativeADBUtils.execute_adb_command(adb_command, timeout)

    @staticmethod
    def get_screen_size(device_id: str) -> Tuple[int, int]:
        """
        获取设备屏幕尺寸
        
        Args:
            device_id: 设备ID
            
        Returns:
            屏幕尺寸 (width, height)
        """
        success, output = NativeADBUtils.shell_command(device_id, "wm size", timeout=10)

        if success and ":" in output:
            size_part = output.split(":")[-1].strip()
            if "x" in size_part:
                try:
                    width, height = map(int, size_part.split("x"))
                    logger.info(f"📱 Screen size detected: {width}x{height}")
                    return width, height
                except ValueError:
                    pass

        logger.warning(f"⚠️ Failed to get screen size for {device_id}, using default: 1080x1920")
        return 1080, 1920

    @staticmethod
    def take_screenshot(device_id: str, local_path: str) -> bool:
        """
        截取设备屏幕截图
        
        Args:
            device_id: 设备ID
            local_path: 本地保存路径
            
        Returns:
            是否成功
        """
        import os
        from datetime import datetime

        # 生成临时文件名
        remote_file = f"/sdcard/temp_screenshot_{datetime.now().strftime('%H%M%S')}.png"

        try:
            # 1. 在设备上截图
            success, output = NativeADBUtils.shell_command(device_id, f"screencap -p {remote_file}")
            if not success:
                logger.error(f"Failed to take screenshot on device: {output}")
                return False

            # 2. 拉取到本地
            pull_command = f"adb -s {device_id} pull {remote_file} {local_path}"
            success, output = NativeADBUtils.execute_adb_command(pull_command)
            if not success:
                logger.error(f"Failed to pull screenshot: {output}")
                return False

            # 3. 删除设备上的临时文件
            NativeADBUtils.shell_command(device_id, f"rm {remote_file}")

            # 4. 检查文件是否成功创建
            if os.path.exists(local_path) and os.path.getsize(local_path) > 0:
                logger.info(f"📸 Screenshot saved: {local_path}")
                return True
            else:
                logger.error(f"Screenshot file not created or empty: {local_path}")
                return False

        except Exception as e:
            logger.error(f"Screenshot failed: {str(e)}")
            return False

    @staticmethod
    def is_remote_device(device_id: str) -> bool:
        """
        判断是否为远程设备
        
        Args:
            device_id: 设备ID
            
        Returns:
            是否为远程设备
        """
        # 远程设备格式：IP:PORT，如 ***********:4657
        return bool(re.match(r'^\d+\.\d+\.\d+\.\d+:\d+$', device_id))


# 全局工具实例
native_adb = NativeADBUtils()


# 便捷函数
def execute_adb(command: str, timeout: int = 30) -> str:
    """
    执行ADB命令的便捷函数
    
    Args:
        command: ADB命令字符串
        timeout: 超时时间（秒）
        
    Returns:
        命令输出，失败返回"ERROR"
    """
    success, output = native_adb.execute_adb_command(command, timeout)
    return output if success else "ERROR"


def connect_device(device_id: str, task_id: str = None) -> bool:
    """
    连接设备的便捷函数
    
    Args:
        device_id: 设备ID
        task_id: 任务ID（用于日志）
        
    Returns:
        是否连接成功
    """
    log_prefix = f"[{task_id}] " if task_id else ""

    # 如果是远程设备，需要先连接
    if native_adb.is_remote_device(device_id):
        logger.info(f"{log_prefix}🔗 Connecting to remote device: {device_id}")

        if not native_adb.connect_device(device_id):
            return False

        # 等待连接稳定
        time.sleep(2)

    # 验证设备是否可用
    if native_adb.is_device_available(device_id):
        logger.info(f"{log_prefix}✅ Device {device_id} is ready for use")
        return True
    else:
        logger.error(f"{log_prefix}❌ Device {device_id} is not available after connection")
        return False


def disconnect_device(device_id: str, task_id: str = None) -> bool:
    """
    断开设备连接的便捷函数
    
    Args:
        device_id: 设备ID
        task_id: 任务ID（用于日志）
        
    Returns:
        是否断开成功
    """
    log_prefix = f"[{task_id}] " if task_id else ""

    if native_adb.is_remote_device(device_id):
        logger.info(f"{log_prefix}🔌 Disconnecting remote device: {device_id}")
        return native_adb.disconnect_device(device_id)
    else:
        logger.info(f"{log_prefix}✅ Local device {device_id} state cleaned")
        return True


def is_device_available(device_id: str) -> bool:
    """
    检查设备是否可用的便捷函数
    
    Args:
        device_id: 设备ID

    Returns:
        设备是否可用
    """
    return native_adb.is_device_available(device_id)
